#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Meg<PERSON>jtó Feltérképező Alkalmazás
Windows meghajtók elemzése és grafikus megjelenítése
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import shutil
import platform
import threading
from pathlib import Path
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np
import time
from collections import defaultdict

class DriveAnalyzer:
    def __init__(self, root):
        self.root = root
        self.root.title("Meghajtó Feltérképező")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Változók
        self.selected_drive = tk.StringVar()
        self.drive_info = {}
        self.folder_sizes = {}
        self.analysis_running = False

        self.setup_ui()
        self.refresh_drives()
        
    def setup_ui(self):
        """Felhasználói felület beállítása"""
        # Főkeret
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Meghajtó kiválasztás
        drive_frame = ttk.LabelFrame(main_frame, text="Meghajtó kiválasztása", padding="5")
        drive_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(drive_frame, text="Meghajtó:").grid(row=0, column=0, padx=(0, 5))
        self.drive_combo = ttk.Combobox(drive_frame, textvariable=self.selected_drive, 
                                       state="readonly", width=20)
        self.drive_combo.grid(row=0, column=1, padx=(0, 10))
        self.drive_combo.bind('<<ComboboxSelected>>', self.on_drive_selected)
        
        ttk.Button(drive_frame, text="Frissítés", 
                  command=self.refresh_drives).grid(row=0, column=2, padx=(0, 10))
        ttk.Button(drive_frame, text="Elemzés", 
                  command=self.analyze_drive).grid(row=0, column=3)
        
        # Bal oldali panel - Információk
        info_frame = ttk.LabelFrame(main_frame, text="Meghajtó információk", padding="5")
        info_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        
        # Információs szöveg widget
        self.info_text = tk.Text(info_frame, width=40, height=15, wrap=tk.WORD)
        info_scrollbar = ttk.Scrollbar(info_frame, orient="vertical", command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        info_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Jobb oldali panel - Grafikus megjelenítés
        chart_frame = ttk.LabelFrame(main_frame, text="Grafikus megjelenítés", padding="5")
        chart_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Matplotlib figure
        self.fig = Figure(figsize=(8, 4), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.get_tk_widget().grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Alsó panel - Fájlböngésző
        browser_frame = ttk.LabelFrame(main_frame, text="Fájlböngésző", padding="5")
        browser_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        # TreeView a fájlok megjelenítésére
        self.tree = ttk.Treeview(browser_frame, columns=('size', 'type', 'modified'), show='tree headings')
        self.tree.heading('#0', text='Név')
        self.tree.heading('size', text='Méret')
        self.tree.heading('type', text='Típus')
        self.tree.heading('modified', text='Módosítva')

        self.tree.column('#0', width=300)
        self.tree.column('size', width=100)
        self.tree.column('type', width=100)
        self.tree.column('modified', width=150)

        # Scrollbar a TreeView-hoz
        tree_scrollbar = ttk.Scrollbar(browser_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=tree_scrollbar.set)

        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        tree_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # TreeView események
        self.tree.bind('<<TreeviewOpen>>', self.on_tree_open)

        # Progress bar az elemzéshez
        self.progress_frame = ttk.Frame(main_frame)
        self.progress_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))

        self.progress_label = ttk.Label(self.progress_frame, text="")
        self.progress_label.grid(row=0, column=0, sticky=tk.W)

        self.progress_bar = ttk.Progressbar(self.progress_frame, mode='indeterminate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(2, 0))

        self.progress_frame.columnconfigure(0, weight=1)
        
        # Grid konfigurálás
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=2)
        main_frame.rowconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        info_frame.columnconfigure(0, weight=1)
        info_frame.rowconfigure(0, weight=1)
        chart_frame.columnconfigure(0, weight=1)
        chart_frame.rowconfigure(0, weight=1)
        browser_frame.columnconfigure(0, weight=1)
        browser_frame.rowconfigure(0, weight=1)
        
    def refresh_drives(self):
        """Elérhető meghajtók frissítése"""
        drives = []
        if platform.system() == "Windows":
            # Windows meghajtók keresése
            for letter in "ABCDEFGHIJKLMNOPQRSTUVWXYZ":
                drive_path = f"{letter}:\\"
                if os.path.exists(drive_path):
                    try:
                        # Meghajtó típus és címke lekérése
                        total, used, free = shutil.disk_usage(drive_path)
                        drives.append(f"{letter}: ({self.format_bytes(total)})")
                    except:
                        drives.append(f"{letter}:")
        else:
            # Linux/Mac esetén
            drives = ["/"]
            
        self.drive_combo['values'] = drives
        if drives:
            self.drive_combo.current(0)
            
    def on_drive_selected(self, event=None):
        """Meghajtó kiválasztásakor"""
        self.clear_display()
        
    def analyze_drive(self):
        """Meghajtó elemzése"""
        if not self.selected_drive.get():
            messagebox.showwarning("Figyelem", "Kérlek válassz ki egy meghajtót!")
            return

        if self.analysis_running:
            messagebox.showinfo("Információ", "Elemzés már folyamatban van!")
            return

        # Elemzés külön szálban
        threading.Thread(target=self._analyze_drive_thread, daemon=True).start()
        
    def _analyze_drive_thread(self):
        """Meghajtó elemzése háttérszálban"""
        try:
            self.analysis_running = True
            self.root.after(0, self.start_progress)

            drive_letter = self.selected_drive.get().split(':')[0]
            drive_path = f"{drive_letter}:\\" if platform.system() == "Windows" else "/"

            # Alapinformációk gyűjtése
            total, used, free = shutil.disk_usage(drive_path)

            self.drive_info = {
                'path': drive_path,
                'total': total,
                'used': used,
                'free': free,
                'usage_percent': (used / total) * 100
            }

            # Fájlrendszer feltérképezése
            self.root.after(0, lambda: self.progress_label.config(text="Fájlrendszer feltérképezése..."))
            self.folder_sizes = self.analyze_folder_sizes(drive_path)

            # UI frissítése a főszálban
            self.root.after(0, self.update_display)
            self.root.after(0, self.populate_tree)

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Hiba", f"Hiba történt az elemzés során: {str(e)}"))
        finally:
            self.analysis_running = False
            self.root.after(0, self.stop_progress)
            
    def update_display(self):
        """Megjelenítés frissítése"""
        if not self.drive_info:
            return
            
        # Információs szöveg frissítése
        self.info_text.delete(1.0, tk.END)
        info_text = f"""Meghajtó: {self.drive_info['path']}

Teljes méret: {self.format_bytes(self.drive_info['total'])}
Használt hely: {self.format_bytes(self.drive_info['used'])}
Szabad hely: {self.format_bytes(self.drive_info['free'])}

Telítettség: {self.drive_info['usage_percent']:.1f}%
"""
        self.info_text.insert(1.0, info_text)
        
        # Grafikus megjelenítés frissítése
        self.update_chart()
        
    def update_chart(self):
        """Grafikon frissítése"""
        self.fig.clear()
        
        # Pie chart létrehozása
        ax1 = self.fig.add_subplot(211)
        sizes = [self.drive_info['used'], self.drive_info['free']]
        labels = ['Használt', 'Szabad']
        colors = ['#ff6b6b', '#4ecdc4']
        
        ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        ax1.set_title(f"Meghajtó telítettség - {self.drive_info['path']}")
        
        # Progress bar stílusú megjelenítés
        ax2 = self.fig.add_subplot(212)
        usage_percent = self.drive_info['usage_percent']
        
        ax2.barh(0, usage_percent, color='#ff6b6b', height=0.3)
        ax2.barh(0, 100-usage_percent, left=usage_percent, color='#4ecdc4', height=0.3)
        ax2.set_xlim(0, 100)
        ax2.set_ylim(-0.5, 0.5)
        ax2.set_xlabel('Telítettség (%)')
        ax2.set_title('Telítettség sáv')
        ax2.set_yticks([])
        
        # Szöveg hozzáadása
        ax2.text(usage_percent/2, 0, f'{usage_percent:.1f}%', 
                ha='center', va='center', fontweight='bold', color='white')
        
        self.fig.tight_layout()
        self.canvas.draw()
        
    def clear_display(self):
        """Megjelenítés törlése"""
        self.info_text.delete(1.0, tk.END)
        self.fig.clear()
        self.canvas.draw()
        # TreeView törlése
        for item in self.tree.get_children():
            self.tree.delete(item)

    def start_progress(self):
        """Progress bar indítása"""
        self.progress_label.config(text="Elemzés folyamatban...")
        self.progress_bar.start(10)

    def stop_progress(self):
        """Progress bar leállítása"""
        self.progress_label.config(text="")
        self.progress_bar.stop()

    def analyze_folder_sizes(self, root_path, max_depth=2):
        """Mappák méretének elemzése"""
        folder_sizes = {}

        try:
            for root, dirs, files in os.walk(root_path):
                # Mélység ellenőrzése
                depth = root.replace(root_path, '').count(os.sep)
                if depth > max_depth:
                    dirs[:] = []  # Ne menjen mélyebbre
                    continue

                folder_size = 0
                for file in files:
                    try:
                        file_path = os.path.join(root, file)
                        folder_size += os.path.getsize(file_path)
                    except (OSError, IOError):
                        continue

                if folder_size > 0:
                    folder_sizes[root] = folder_size

        except Exception as e:
            print(f"Hiba a mappa elemzés során: {e}")

        return folder_sizes

    def populate_tree(self):
        """TreeView feltöltése"""
        if not self.drive_info:
            return

        # TreeView törlése
        for item in self.tree.get_children():
            self.tree.delete(item)

        root_path = self.drive_info['path']

        try:
            # Gyökér elem hozzáadása
            root_item = self.tree.insert('', 'end', text=root_path,
                                       values=(self.format_bytes(self.drive_info['total']), 'Meghajtó', ''))

            # Első szintű mappák hozzáadása
            for item in os.listdir(root_path):
                item_path = os.path.join(root_path, item)

                try:
                    if os.path.isdir(item_path):
                        size = self.folder_sizes.get(item_path, 0)
                        modified = time.ctime(os.path.getmtime(item_path))

                        folder_item = self.tree.insert(root_item, 'end', text=item,
                                                     values=(self.format_bytes(size), 'Mappa', modified))

                        # Dummy elem hozzáadása a + jel megjelenítéséhez
                        self.tree.insert(folder_item, 'end', text='')

                    elif os.path.isfile(item_path):
                        size = os.path.getsize(item_path)
                        modified = time.ctime(os.path.getmtime(item_path))
                        ext = os.path.splitext(item)[1] or 'Fájl'

                        self.tree.insert(root_item, 'end', text=item,
                                       values=(self.format_bytes(size), ext, modified))

                except (OSError, IOError):
                    continue

            # Gyökér elem kinyitása
            self.tree.item(root_item, open=True)

        except Exception as e:
            messagebox.showerror("Hiba", f"Hiba a fájlböngésző feltöltése során: {str(e)}")

    def on_tree_open(self, event):
        """TreeView elem kinyitásakor"""
        item = self.tree.selection()[0]
        children = self.tree.get_children(item)

        # Ha van dummy elem, töröljük és töltjük fel a valódi tartalommal
        if len(children) == 1 and self.tree.item(children[0])['text'] == '':
            self.tree.delete(children[0])

            # Teljes útvonal meghatározása
            path_parts = []
            current = item
            while current:
                text = self.tree.item(current)['text']
                if text:
                    path_parts.append(text)
                current = self.tree.parent(current)

            path_parts.reverse()
            full_path = os.path.join(*path_parts) if len(path_parts) > 1 else path_parts[0]

            try:
                for sub_item in os.listdir(full_path):
                    sub_path = os.path.join(full_path, sub_item)

                    try:
                        if os.path.isdir(sub_path):
                            size = self.folder_sizes.get(sub_path, 0)
                            modified = time.ctime(os.path.getmtime(sub_path))

                            folder_item = self.tree.insert(item, 'end', text=sub_item,
                                                         values=(self.format_bytes(size), 'Mappa', modified))

                            # Dummy elem hozzáadása
                            self.tree.insert(folder_item, 'end', text='')

                        elif os.path.isfile(sub_path):
                            size = os.path.getsize(sub_path)
                            modified = time.ctime(os.path.getmtime(sub_path))
                            ext = os.path.splitext(sub_item)[1] or 'Fájl'

                            self.tree.insert(item, 'end', text=sub_item,
                                           values=(self.format_bytes(size), ext, modified))

                    except (OSError, IOError):
                        continue

            except Exception as e:
                print(f"Hiba a mappa tartalmának betöltése során: {e}")

    @staticmethod
    def format_bytes(bytes_value):
        """Bájtok formázása olvasható formátumba"""
        if bytes_value == 0:
            return "0 B"
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.2f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.2f} PB"

def main():
    root = tk.Tk()
    app = DriveAnalyzer(root)
    root.mainloop()

if __name__ == "__main__":
    main()
