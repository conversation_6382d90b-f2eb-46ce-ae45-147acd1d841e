# Meghajtó Feltérképező Alkalmazás

Egy Windows ablakos alkalmazás, amely feltérképezi a kiválasztott meghajtót és grafikusan megjeleníti annak adatait és telítettségét.

## Funkciók

- **Meghajtó kiválasztás**: Dropdown menüből választható ki a vizsgálni kívánt meghajtó
- **Grafikus megjelenítés**: 
  - Pie chart a meghajtó telítettségének megjelenítésére
  - Progress bar stílusú telítettség mutató
- **Részletes információk**: 
  - Tel<PERSON>s méret, hasz<PERSON>lt hely, szabad hely
  - Telítettség százalékban
- **Fájlböngésző**: 
  - Hierarchikus megjelenítés TreeView komponenssel
  - Fájl és mappa méretek
  - Módosítási dátumok
  - Fájl típusok megjelenítése

## Telepítés

1. Klónozd le a projektet vagy töltsd le a fájlokat
2. Telepítsd a szükséges függőségeket:
   ```bash
   pip install -r requirements.txt
   ```

## Használat

Az alkalmazás indítása:
```bash
python run.py
```

vagy közvetlenül:
```bash
python drive_analyzer.py
```

### Használati útmutató

1. **Meghajtó kiválasztása**: 
   - A felső dropdown menüből válaszd ki a vizsgálni kívánt meghajtót
   - Kattints a "Frissítés" gombra az elérhető meghajtók frissítéséhez

2. **Elemzés indítása**:
   - Kattints az "Elemzés" gombra
   - Az alkalmazás háttérben elemzi a meghajtót
   - A progress bar mutatja az elemzés állapotát

3. **Eredmények megtekintése**:
   - **Bal panel**: Részletes információk szöveges formában
   - **Jobb felső panel**: Grafikus megjelenítés (pie chart és progress bar)
   - **Alsó panel**: Fájlböngésző hierarchikus megjelenítéssel

4. **Fájlböngésző használata**:
   - Kattints a mappák melletti + jelre a tartalom kibontásához
   - A fájlok és mappák mérete, típusa és módosítási dátuma látható

## Rendszerkövetelmények

- **Operációs rendszer**: Windows (elsősorban), Linux/Mac (korlátozott támogatás)
- **Python**: 3.7 vagy újabb
- **Függőségek**: 
  - tkinter (általában a Python-nal együtt települ)
  - matplotlib >= 3.5.0
  - numpy >= 1.21.0

## Funkciók részletesen

### Meghajtó elemzés
- Automatikus meghajtó felismerés Windows rendszeren
- Teljes méret, használt és szabad hely számítása
- Telítettség százalék kiszámítása

### Grafikus megjelenítés
- **Pie chart**: Használt vs. szabad hely arányának megjelenítése
- **Progress bar**: Lineáris telítettség mutató százalékos értékkel
- Színkódolt megjelenítés (piros = használt, türkiz = szabad)

### Fájlrendszer böngészés
- Hierarchikus mappastruktúra megjelenítése
- Lazy loading: mappák tartalma csak kinyitáskor töltődik be
- Fájl és mappa méretek megjelenítése
- Fájltípus felismerés kiterjesztés alapján
- Módosítási dátumok megjelenítése

### Teljesítmény optimalizálás
- Háttérszálas elemzés a UI blokkolásának elkerülésére
- Korlátozott mélységű mappaböngészés a teljesítmény megőrzésére
- Progress bar az elemzés állapotának jelzésére

## Hibakezelés

Az alkalmazás robosztus hibakezelést tartalmaz:
- Hozzáférési jogosultság problémák kezelése
- Nem létező fájlok/mappák kihagyása
- Felhasználóbarát hibaüzenetek
- Graceful degradation hiba esetén

## Fejlesztési lehetőségek

- Több meghajtó egyidejű elemzése
- Fájltípus szerinti csoportosítás
- Exportálási funkciók (CSV, JSON)
- Részletesebb fájlstatisztikák
- Keresési funkció
- Duplikált fájlok keresése

## Licenc

Ez a projekt oktatási célokra készült. Szabadon használható és módosítható.
