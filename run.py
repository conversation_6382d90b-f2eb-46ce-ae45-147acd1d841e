#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Meghajtó Feltérképező Alkalmazás - Indító script
"""

import sys
import os

# Ellen<PERSON>rizzük, hogy minden szükséges modul elérhető-e
try:
    import tkinter as tk
    from tkinter import ttk, messagebox
    import matplotlib.pyplot as plt
    import numpy as np
except ImportError as e:
    print(f"Hiányzó modul: {e}")
    print("Kérlek telepítsd a szükséges modulokat:")
    print("pip install -r requirements.txt")
    sys.exit(1)

# Importáljuk a fő alkalmazást
try:
    from drive_analyzer import DriveAnalyzer
except ImportError:
    print("Hiba: drive_analyzer.py nem található!")
    sys.exit(1)

def main():
    """Fő függvény"""
    try:
        # Tkinter root ablak létrehozása
        root = tk.Tk()
        
        # Alkalmazás indítása
        app = DriveAnalyzer(root)
        
        # Főciklus indítása
        root.mainloop()
        
    except Exception as e:
        print(f"Hiba az alkalmazás indítása során: {e}")
        messagebox.showerror("Hiba", f"Hiba az alkalmazás indítása során: {e}")

if __name__ == "__main__":
    main()
